<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                </flux:breadcrumbs.item>
                <flux:breadcrumbs.item current>{{ __('Accountants Management') }}</flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>

        <!-- Add New Button -->
        <div>
            <flux:button variant="primary" :href="route('accountants.create')" wire:navigate icon="plus">
                {{ __('Add New Accountant') }}
            </flux:button>
        </div>
    </div>

    <!-- Filters -->
    <div class="mb-6 rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
            <flux:input wire:model.live="search" :placeholder="__('Search accountants...')" icon="magnifying-glass" />

            <flux:select wire:model.live="status" :placeholder="__('Filter by status')">
                <flux:option value="">{{ __('All Statuses') }}</flux:option>
                <flux:option value="1">{{ __('Active') }}</flux:option>
                <flux:option value="0">{{ __('Inactive') }}</flux:option>
            </flux:select>

            <div class="flex justify-end">
                <flux:button variant="outline" wire:click="$refresh" icon="arrow-path">
                    {{ __('Refresh') }}
                </flux:button>
            </div>
        </div>
    </div>

    <!-- Accountants Table -->
    <div class="overflow-hidden rounded-lg border border-zinc-200 bg-white dark:border-zinc-700 dark:bg-zinc-900">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-800">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Accountant') }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Email') }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Created') }}
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-900">
                    @forelse ($accountants as $accountant)
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-800">
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                <div>
                                    <div class="font-semibold">{{ $accountant->name }}</div>
                                    <div class="text-xs text-zinc-500">{{ $accountant->accountant_id }}</div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $accountant->email }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if ($accountant->status === 1)
                                    <flux:badge variant="success">{{ __('Active') }}</flux:badge>
                                @else
                                    <flux:badge variant="danger">{{ __('Inactive') }}</flux:badge>
                                @endif
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-500 dark:text-zinc-400">
                                {{ $accountant->inserted ? $accountant->inserted->format('M d, Y') : '-' }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    <flux:modal.trigger name="view-accountant-{{ $accountant->accountant_id }}">
                                        <flux:button variant="ghost" size="xs" icon="eye" />
                                    </flux:modal.trigger>

                                    <flux:button variant="ghost" size="xs" icon="pencil"
                                        :href="route('accountants.edit', $accountant->accountant_id)" wire:navigate />

                                    <flux:button variant="ghost" size="xs" icon="trash"
                                        wire:click="confirmDelete('{{ $accountant->accountant_id }}')" />
                                </div>
                            </td>
                        </tr>

                        <!-- View Modal -->
                        <flux:modal name="view-accountant-{{ $accountant->accountant_id }}" class="md:w-96">
                            <div class="space-y-6">
                                <div>
                                    <flux:heading size="lg">{{ __('Accountant Details') }}</flux:heading>
                                    <flux:subheading>{{ __('View accountant information') }}</flux:subheading>
                                </div>

                                <div class="space-y-4">
                                    <div>
                                        <flux:label>{{ __('Accountant ID') }}</flux:label>
                                        <flux:input value="{{ $accountant->accountant_id }}" readonly />
                                    </div>

                                    <div>
                                        <flux:label>{{ __('Name') }}</flux:label>
                                        <flux:input value="{{ $accountant->name }}" readonly />
                                    </div>

                                    <div>
                                        <flux:label>{{ __('Email') }}</flux:label>
                                        <flux:input value="{{ $accountant->email }}" readonly />
                                    </div>

                                    <div>
                                        <flux:label>{{ __('Status') }}</flux:label>
                                        <flux:input value="{{ $accountant->status ? 'Active' : 'Inactive' }}" readonly />
                                    </div>

                                    <div>
                                        <flux:label>{{ __('Created') }}</flux:label>
                                        <flux:input value="{{ $accountant->inserted ? $accountant->inserted->format('M d, Y H:i') : '-' }}" readonly />
                                    </div>
                                </div>

                                <div class="flex justify-end space-x-2">
                                    <flux:modal.close>
                                        <flux:button variant="outline">{{ __('Close') }}</flux:button>
                                    </flux:modal.close>
                                </div>
                            </div>
                        </flux:modal>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No accountants found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $accountants->links() }}
    </div>

    <!-- Delete Confirmation Modal -->
    <flux:modal name="confirm-accountant-deletion" class="md:w-96">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">{{ __('Delete Accountant') }}</flux:heading>
                <flux:subheading>{{ __('Are you sure you want to delete this accountant? This action cannot be undone.') }}</flux:subheading>
            </div>

            <div class="flex justify-end space-x-2">
                <flux:modal.close>
                    <flux:button variant="outline">{{ __('Cancel') }}</flux:button>
                </flux:modal.close>

                <flux:button variant="danger" wire:click="deleteAccountant">
                    {{ __('Delete') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>
</div>
