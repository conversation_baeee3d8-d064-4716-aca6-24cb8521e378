<div class="w-full">
    <div class="mb-6">
        <flux:breadcrumbs class="flex-wrap">
            <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item :href="route('accountants.index')" wire:navigate>{{ __('Accountants Management') }}
            </flux:breadcrumbs.item>
            <flux:breadcrumbs.item current>
                {{ $isEdit ? __('Edit Accountant') : __('Add New Accountant') }}
            </flux:breadcrumbs.item>
        </flux:breadcrumbs>
    </div>

    <div class="rounded-lg border border-zinc-200 bg-white p-6 dark:border-zinc-700 dark:bg-zinc-900">
        <form wire:submit="save" class="space-y-6">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <!-- Basic Information Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Basic Information') }}</flux:heading>

                    @if ($isEdit)
                        <flux:input wire:model="accountantId" :label="__('Accountant ID')" disabled readonly />
                    @endif

                    <flux:input wire:model="name" :label="__('Name')" required :error="$errors->first('name')" />

                    <flux:input wire:model="email" :label="__('Email')" type="email" required
                        :error="$errors->first('email')" />
                </div>

                <!-- Security & Status Section -->
                <div class="space-y-6">
                    <flux:heading size="sm">{{ __('Security & Status') }}</flux:heading>

                    <flux:input wire:model="password" :label="$isEdit ? __('New Password (leave blank to keep current)') : __('Password')"
                        type="password" :required="!$isEdit" :error="$errors->first('password')" />

                    <flux:input wire:model="password_confirmation" :label="__('Confirm Password')"
                        type="password" :required="!$isEdit" :error="$errors->first('password_confirmation')" />

                    <flux:select wire:model="status" :label="__('Status')" required :error="$errors->first('status')">
                        <flux:option value="1">{{ __('Active') }}</flux:option>
                        <flux:option value="0">{{ __('Inactive') }}</flux:option>
                    </flux:select>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="outline" :href="route('accountants.index')" wire:navigate>
                    {{ __('Cancel') }}
                </flux:button>

                <flux:button variant="primary" type="submit">
                    {{ $isEdit ? __('Update Accountant') : __('Create Accountant') }}
                </flux:button>
            </div>
        </form>
    </div>
</div>
