<div class="w-full">
    <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
        <!-- Breadcrumb -->
        <div>
            <flux:breadcrumbs class="flex-wrap">
                @if (!$isCommercialUser)
                    <flux:breadcrumbs.item :href="route('dashboard')" wire:navigate>{{ __('Dashboard') }}
                    </flux:breadcrumbs.item>
                @endif
                <flux:breadcrumbs.item current>
                    {{ $isCommercialUser ? __('My Commercial Balance') : __('Commercials Balance & Payment') }}
                </flux:breadcrumbs.item>
            </flux:breadcrumbs>
        </div>
    </div>

    @if (!$isCommercialUser)
        <!-- Search and Filters (only for admin users) -->
        <div class="mb-6 flex flex-col space-y-4 md:flex-row md:items-center md:space-x-4 md:space-y-0">
            <div class="flex-1">
                <flux:input wire:model.live.debounce.300ms="search" placeholder="{{ __('Search commercials...') }}"
                    icon="magnifying-glass" />
            </div>
            <div class="w-full md:w-48">
                <flux:select wire:model.live="status">
                    <option value="">{{ __('All Status') }}</option>
                    <option value="1">{{ __('Active') }}</option>
                    <option value="0">{{ __('Inactive') }}</option>
                </flux:select>
            </div>
        </div>
    @endif

    <!-- Commercials Table -->
    <div
        class="overflow-hidden rounded-lg border border-zinc-200 bg-white shadow dark:border-zinc-700 dark:bg-zinc-800">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                <thead class="bg-zinc-50 dark:bg-zinc-900">
                    <tr>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Commercial Name') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Status') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Purchased Tokens') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Redeemed') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid-Redeemed') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid Amounts') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Paid Currency') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Balance') }}
                        </th>
                        <th
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                    @forelse($commercials as $commercial)
                        <!-- Commercial Summary Row -->
                        <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700 bg-zinc-50 dark:bg-zinc-900 font-semibold">
                            <td
                                class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                {{-- toggle button --}}
                                {{-- <button @click="showClubDetails = !showClubDetails">
                                    <flux:icon.chevron-down class="w-4 h-4" x-show="!showClubDetails" />
                                    <flux:icon.chevron-up class="w-4 h-4" x-show="showClubDetails" /> --}}
                                </button>
                                <div>
                                    <div class="font-semibold">{{ $commercial->name }} (Total)</div>
                                    <div class="text-xs text-zinc-500">{{ $commercial->commercial_id }}</div>
                                </div>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <flux:tooltip
                                    content="{{ $commercial->status == 1 ? 'Deactivate Commercial' : 'Activate Commercial' }}">
                                    <button wire:click="toggleCommercialStatus('{{ $commercial->commercial_id }}')"
                                        class="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium
                                           {{ $commercial->status === 1
                                               ? 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:hover:bg-green-800 dark:text-green-200'
                                               : 'bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 dark:text-red-200' }}
                                           transition-colors duration-200 cursor-pointer">
                                        {{ $commercial->status === 1 ? 'Active' : 'Inactive' }}
                                    </button>
                                </flux:tooltip>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($commercial->stats['purchased'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($commercial->stats['redeemed'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($commercial->stats['paid_redeemed'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                €{{ number_format($commercial->stats['paid_amounts'], 2) }}
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                EUR
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-semibold">
                                <span
                                    class="{{ $commercial->stats['balance'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                    €{{ number_format($commercial->stats['balance'], 2) }}
                                </span>
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                <div class="flex space-x-2">
                                    @if (auth('admin')->check())
                                        @if (!$isCommercialUser)
                                            @php
                                                $clubIds = \App\Models\Club::where(
                                                    'commercial_id',
                                                    $commercial->commercial_id,
                                                )
                                                    ->pluck('club_id')
                                                    ->toArray();
                                                $tokensToPayCount = !empty($clubIds)
                                                    ? \App\Models\Token::whereIn('club_id', $clubIds)
                                                        ->whereNotNull('purchased_at')
                                                        ->whereNull('paid_at')
                                                        ->count()
                                                    : 0;
                                            @endphp
                                            @if ($tokensToPayCount > 0)
                                                <flux:button variant="primary" size="xs"
                                                    wire:click="payCommercial('{{ $commercial->commercial_id }}')">
                                                    {{ __('Pay Commercial') }}
                                                </flux:button>
                                            @else
                                                <span class="text-zinc-400 text-xs">{{ __('No tokens to pay') }}</span>
                                            @endif
                                        @endif
                                    @endif


                                    <flux:button variant="outline" size="xs"
                                        wire:click="viewClubDetails('{{ $commercial->commercial_id }}')">
                                        {{ __('View Club Details') }}
                                    </flux:button>
                                </div>
                            </td>
                        </tr>

                        <!-- Club Breakdown Rows -->
                        {{-- @if (count($commercial->clubBreakdown) > 0)
                            @foreach ($commercial->clubBreakdown as $clubData)
                                <tr x-show="showClubDetails" class="hover:bg-zinc-50 dark:hover:bg-zinc-700 text-sm">
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        <div class="pl-4">
                                            <div class="font-medium">{{ $clubData['club_name'] }}</div>
                                            <div class="text-xs text-zinc-500">{{ $clubData['club_id'] }}</div>
                                        </div>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        <!-- Status column - empty for club rows -->
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        €{{ number_format($clubData['purchased'], 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        €{{ number_format($clubData['redeemed'], 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        €{{ number_format($clubData['paid_redeemed'], 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        €{{ number_format($clubData['paid_amounts'], 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm text-zinc-700 dark:text-zinc-300">
                                        EUR
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm">
                                        <span
                                            class="{{ $clubData['balance'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                            €{{ number_format($clubData['balance'], 2) }}
                                        </span>
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-3 text-sm">
                                        <!-- Actions column - empty for club rows -->
                                    </td>
                                </tr>
                            @endforeach
                        @endif --}}
                    @empty
                        <tr>
                            <td colspan="9" class="px-6 py-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                                {{ __('No commercials found.') }}
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="mt-6">
        {{ $commercials->links() }}
    </div>

    <!-- Payment Modal -->
    <flux:modal name="payment-modal" wire:model="showPaymentModal" class="w-full md:max-w-2xl">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
                {{ __('Pay Commercial') }} - {{ $selectedCommercialForPayment->name ?? '' }}
            </h3>

            <div class="space-y-4">
                <div>
                    <p class="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
                        {{ __('Select payment type for this commercial:') }}
                    </p>

                    <flux:select wire:model="paymentType" label="{{ __('Payment Type') }}">
                        <option value="0">{{ __('Wire') }}</option>
                        <option value="1">{{ __('Cash') }}</option>
                    </flux:select>
                </div>

                @if ($selectedCommercialForPayment)
                    @php
                        $clubIds = \App\Models\Club::where(
                            'commercial_id',
                            $selectedCommercialForPayment->commercial_id,
                        )
                            ->pluck('club_id')
                            ->toArray();
                        $tokensToPayCount = !empty($clubIds)
                            ? \App\Models\Token::whereIn('club_id', $clubIds)
                                ->whereNotNull('purchased_at')
                                // ->whereNull('paid_at')
                                ->count()
                            : 0;

                        $tokensValue = 0;
                        if (!empty($clubIds)) {
                            $tokens = \App\Models\Token::whereIn('club_id', $clubIds)
                                ->whereNotNull('purchased_at')
                                ->whereNull('paid_at')
                                ->with(['tokenType', 'club'])
                                ->get();

                            foreach ($tokens as $token) {
                                $tokenValue = $token->tokenType ? $token->tokenType->value_in_eur : 0;
                                $club = $token->club;
                                // $commercialCommission = $club ? $club->commercial_commission / 100 : 0;
                                $tokensValue += $tokenValue * 0.03;
                            }
                        }
                    @endphp

                    <div class="bg-zinc-50 dark:bg-zinc-900 p-4 rounded-lg">
                        <div class="text-sm text-zinc-600 dark:text-zinc-400">
                            <p><strong>{{ __('Tokens to pay:') }}</strong> {{ $tokensToPayCount }}</p>
                            <p><strong>{{ __('Total amount:') }}</strong> €{{ number_format($tokensValue, 2) }}</p>
                            <p><strong>{{ __('Commercial ID:') }}</strong>
                                {{ $selectedCommercialForPayment->commercial_id }}</p>
                        </div>
                    </div>
                @endif
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <flux:button variant="ghost" wire:click="closePaymentModal">
                    {{ __('Cancel') }}
                </flux:button>
                <flux:button variant="primary" wire:click="processPayment">
                    {{ __('Submit Payment') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Club Details Modal -->
    <flux:modal name="club-details-modal" wire:model="showClubDetailsModal" class="w-full md:max-w-6xl">
        <div class="p-6">
            <h3 class="text-lg font-semibold text-zinc-900 dark:text-zinc-100 mb-4">
                {{ __('Club Details') }} - {{ $selectedCommercialForDetails->name ?? '' }}
            </h3>

            @if ($selectedCommercialForDetails && count($clubDetails) > 0)
                <div
                    class="overflow-hidden rounded-lg border border-zinc-200 bg-white shadow dark:border-zinc-700 dark:bg-zinc-800">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-zinc-200 dark:divide-zinc-700">
                            <thead class="bg-zinc-50 dark:bg-zinc-900">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Club Name') }}
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Purchased Tokens') }}
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Redeemed') }}
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Paid-Redeemed') }}
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Paid Amounts') }}
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-zinc-500 dark:text-zinc-400">
                                        {{ __('Revenue (3%)') }}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-zinc-200 bg-white dark:divide-zinc-700 dark:bg-zinc-800">
                                @foreach ($clubDetails as $clubData)
                                    <tr class="hover:bg-zinc-50 dark:hover:bg-zinc-700">
                                        <td
                                            class="whitespace-nowrap px-6 py-4 text-sm font-medium text-zinc-900 dark:text-zinc-100">
                                            <div>
                                                <div class="font-semibold">{{ $clubData['club_name'] }}</div>
                                                <div class="text-xs text-zinc-500">{{ $clubData['club_id'] }}</div>
                                            </div>
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                            €{{ number_format($clubData['purchased'], 2) }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                            €{{ number_format($clubData['redeemed'], 2) }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                            €{{ number_format($clubData['paid_redeemed'], 2) }}
                                        </td>
                                        <td
                                            class="whitespace-nowrap px-6 py-4 text-sm text-zinc-900 dark:text-zinc-100">
                                            €{{ number_format($clubData['paid_amounts'], 2) }}
                                        </td>
                                        <td class="whitespace-nowrap px-6 py-4 text-sm font-semibold">
                                            <span
                                                class="{{ $clubData['balance'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                                €{{ number_format($clubData['balance'], 2) }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach

                                <!-- Total Row -->
                                @php
                                    $totalPurchased = collect($clubDetails)->sum('purchased');
                                    $totalRedeemed = collect($clubDetails)->sum('redeemed');
                                    $totalPaidRedeemed = collect($clubDetails)->sum('paid_redeemed');
                                    $totalPaidAmounts = collect($clubDetails)->sum('paid_amounts');
                                    $totalBalance = collect($clubDetails)->sum('balance');
                                @endphp
                                <tr class="bg-zinc-100 dark:bg-zinc-700 font-semibold">
                                    <td
                                        class="whitespace-nowrap px-6 py-4 text-sm font-bold text-zinc-900 dark:text-zinc-100">
                                        {{ __('TOTAL') }}
                                    </td>
                                    <td
                                        class="whitespace-nowrap px-6 py-4 text-sm font-bold text-zinc-900 dark:text-zinc-100">
                                        €{{ number_format($totalPurchased, 2) }}
                                    </td>
                                    <td
                                        class="whitespace-nowrap px-6 py-4 text-sm font-bold text-zinc-900 dark:text-zinc-100">
                                        €{{ number_format($totalRedeemed, 2) }}
                                    </td>
                                    <td
                                        class="whitespace-nowrap px-6 py-4 text-sm font-bold text-zinc-900 dark:text-zinc-100">
                                        €{{ number_format($totalPaidRedeemed, 2) }}
                                    </td>
                                    <td
                                        class="whitespace-nowrap px-6 py-4 text-sm font-bold text-zinc-900 dark:text-zinc-100">
                                        €{{ number_format($totalPaidAmounts, 2) }}
                                    </td>
                                    <td class="whitespace-nowrap px-6 py-4 text-sm font-bold">
                                        <span
                                            class="{{ $totalBalance >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }}">
                                            €{{ number_format($totalBalance, 2) }}
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            @else
                <div class="text-center py-8">
                    <p class="text-zinc-500 dark:text-zinc-400">
                        {{ __('No club data available for this commercial.') }}</p>
                </div>
            @endif

            <div class="mt-6 flex justify-end">
                <flux:button variant="ghost" wire:click="closeClubDetailsModal">
                    {{ __('Close') }}
                </flux:button>
            </div>
        </div>
    </flux:modal>

    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="rounded-md bg-green-50 p-4 dark:bg-green-900">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800 dark:text-green-200">
                            {{ session('message') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="fixed bottom-4 right-4 z-50">
            <div class="rounded-md bg-red-50 p-4 dark:bg-red-900">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800 dark:text-red-200">
                            {{ session('error') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
