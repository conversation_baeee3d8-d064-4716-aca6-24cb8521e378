<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AccountantMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated as accountant (web session)
        if (!Auth::guard('accountant_web')->check()) {
            // If it's an AJAX request, return JSON response
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized. Accountant access required.'
                ], 403);
            }

            // Redirect to accountant subdomain login for web requests
            $accountantLoginUrl = 'https://accounting.' . parse_url(config('app.url'), PHP_URL_HOST);
            return redirect($accountantLoginUrl)->with('error', 'Unauthorized. Accountant access required.');
        }

        return $next($request);
    }
}
