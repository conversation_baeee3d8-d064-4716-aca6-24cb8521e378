<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Illuminate\Contracts\Auth\CanResetPassword;
use Illuminate\Auth\Passwords\CanResetPassword as CanResetPasswordTrait;



class Commercial extends Authenticatable implements CanResetPassword
{
    use Notifiable, CanResetPasswordTrait;

    protected $primaryKey = 'commercial_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'commercial_id',
        'inserted',
        'tax_id',
        'name',
        'phone',
        'email',
        'password',
        'postal_code',
        'country',
        'status',
        'remember_token'
    ];

    protected $casts = [
        'inserted' => 'datetime',
        'status' => 'integer',
    ];

    protected $hidden = [
        'password',
        'remember_token'
    ];

    /**
     * Generate unique commercial ID
     * Format: 6 characters alphanumeric (letters and numbers)
     */
    public static function generateCommercialId(): string
    {
        do {
            // Generate 6 random alphanumeric characters
            $commercialId = 'COM' . strtoupper(Str::random(3));
        } while (self::where('commercial_id', $commercialId)->exists());

        return $commercialId;
    }

    /**
     * Get clubs associated with this commercial
     */
    public function clubs()
    {
        return $this->hasMany(Club::class, 'commercial_id', 'commercial_id');
    }

    /**
     * Get sponsors associated with this commercial
     */
    public function sponsors()
    {
        return $this->hasMany(Sponsor::class, 'commercial_id', 'commercial_id');
    }
}
