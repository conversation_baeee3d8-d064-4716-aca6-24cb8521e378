<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Accountant extends Authenticatable
{
    use HasFactory;

    protected $primaryKey = 'accountant_id';
    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false;

    protected $fillable = [
        'accountant_id',
        'name',
        'email',
        'password',
        'status',
    ];

    protected $hidden = [
        'password',
    ];

    protected $casts = [
        'status' => 'integer',
        'inserted' => 'datetime',
        'updated' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($accountant) {
            if (empty($accountant->accountant_id)) {
                $accountant->accountant_id = self::generateAccountantId();
            }
        });
    }

    /**
     * Generate a unique 6-character accountant ID
     */
    public static function generateAccountantId(): string
    {
        do {
            $id = strtoupper(Str::random(6));
        } while (self::where('accountant_id', $id)->exists());

        return $id;
    }

    /**
     * Get the accountant's initials for display
     */
    public function initials(): string
    {
        $words = explode(' ', $this->name);
        $initials = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper($word[0]);
            }
        }

        return $initials ?: strtoupper(substr($this->name, 0, 2));
    }
}
