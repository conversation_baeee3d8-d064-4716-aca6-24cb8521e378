<?php

namespace App\Livewire\Accountants;

use App\Models\Accountant;
use Livewire\Component;
use Livewire\WithPagination;

class AccountantManagement extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $deleteId;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
    ];

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function render()
    {
        $query = Accountant::query();

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                    ->orWhere('accountant_id', 'like', '%' . $this->search . '%')
                    ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->status !== '') {
            $query->where('status', $this->status);
        }

        $accountants = $query->latest('inserted')->paginate(10);

        return view('livewire.accountants.accountant-management', [
            'accountants' => $accountants
        ]);
    }

    public function confirmDelete($accountantId)
    {
        $this->deleteId = $accountantId;
        $this->modal('confirm-accountant-deletion')->show();
    }

    public function deleteAccountant()
    {
        $accountant = Accountant::findOrFail($this->deleteId);
        $accountant->delete();

        session()->flash('message', 'Accountant deleted successfully.');
        $this->modal('confirm-accountant-deletion')->close();
    }
}
