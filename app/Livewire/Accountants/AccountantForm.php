<?php

namespace App\Livewire\Accountants;

use App\Models\Accountant;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class AccountantForm extends Component
{
    public $accountantId;
    public $isEdit = false;

    // Form fields
    public $name = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';
    public $status = 1;

    protected $rules = [
        'name' => 'required|string|max:50',
        'email' => 'required|email|max:30',
        'password' => 'required|string|min:8|confirmed',
        'status' => 'required|integer|in:0,1',
    ];

    public function mount($accountantId = null)
    {
        $this->accountantId = $accountantId;
        $this->isEdit = !is_null($accountantId);

        if ($this->isEdit) {
            $this->loadAccountant();
        }
    }

    public function loadAccountant()
    {
        $accountant = Accountant::findOrFail($this->accountantId);

        $this->name = $accountant->name;
        $this->email = $accountant->email;
        $this->status = $accountant->status;

        // Don't load password for security
        $this->password = '';
        $this->password_confirmation = '';
    }

    public function updatedEmail()
    {
        if ($this->isEdit) {
            $this->validateOnly('email', [
                'email' => 'required|email|max:30|unique:accountants,email,' . $this->accountantId . ',accountant_id'
            ]);
        } else {
            $this->validateOnly('email', [
                'email' => 'required|email|max:30|unique:accountants,email'
            ]);
        }
    }

    public function save()
    {
        // Adjust validation rules for edit mode
        $rules = $this->rules;

        if ($this->isEdit) {
            // For edit, password is optional
            if (empty($this->password)) {
                unset($rules['password']);
            }

            // Make email unique except for current record
            $rules['email'] = 'required|email|max:30|unique:accountants,email,' . $this->accountantId . ',accountant_id';
        } else {
            // For create, email must be unique
            $rules['email'] = 'required|email|max:30|unique:accountants,email';
        }

        $this->validate($rules);

        if ($this->isEdit) {
            $accountant = Accountant::findOrFail($this->accountantId);

            $accountant->name = $this->name;
            $accountant->email = $this->email;
            $accountant->status = $this->status;

            // Only update password if provided
            if (!empty($this->password)) {
                $accountant->password = Hash::make($this->password);
            }

            $accountant->save();

            session()->flash('message', 'Accountant updated successfully.');
        } else {
            Accountant::create([
                'name' => $this->name,
                'email' => $this->email,
                'password' => Hash::make($this->password),
                'status' => $this->status,
            ]);

            session()->flash('message', 'Accountant created successfully.');
        }

        return redirect()->route('accountants.index');
    }

    public function render()
    {
        return view('livewire.accountants.accountant-form');
    }
}
