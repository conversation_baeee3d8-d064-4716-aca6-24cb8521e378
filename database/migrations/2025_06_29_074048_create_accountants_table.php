<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accountants', function (Blueprint $table) {
            $table->string('accountant_id', 6)->primary();
            $table->string('name', 50);
            $table->string('email', 30)->unique();
            $table->string('password');
            $table->tinyInteger('status')->default(1); // 1 = Active, 0 = Inactive
            $table->timestamp('inserted')->useCurrent();
            $table->timestamp('updated')->useCurrent()->useCurrentOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accountants');
    }
};
